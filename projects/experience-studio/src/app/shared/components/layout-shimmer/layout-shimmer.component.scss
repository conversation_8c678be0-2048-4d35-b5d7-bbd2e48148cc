// Base layout structure
.layout-shimmer {
  width: 100%;
  height: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow: hidden;

  // Enhanced hardware acceleration and performance optimization
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // Layout containment for better resize performance
  contain: layout style paint;
  content-visibility: auto;

  // Light theme
  &.theme-light {
    background: #f8f9fa;

    .shimmer-element {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
    }

    .header {
      background: white;
      border-bottom: 1px solid #e5e7eb;
    }

    .sidebar {
      background: #f9fafb;
      border-color: #e5e7eb;
    }

    .body {
      background: white;
    }

    .footer {
      background: #111827;

      .shimmer-element {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
      }
    }
  }

  // Dark theme - Enhanced brightness and contrast
  &.theme-dark {
    background: #1a1a1a;

    .shimmer-element {
      background: linear-gradient(90deg, #404040 25%, #606060 50%, #404040 75%);
      background-size: 200% 100%;
    }

    .header {
      background: #2d2d2d;
      border-bottom: 1px solid #505050;
    }

    .sidebar {
      background: #252525;
      border-color: #505050;
    }

    .body {
      background: #2d2d2d;
    }

    .footer {
      background: #0f0f0f;

      .shimmer-element {
        background: linear-gradient(90deg, #353535 25%, #555555 50%, #353535 75%);
        background-size: 200% 100%;
      }
    }
  }

  // Animation - optimized for performance
  &.animated .shimmer-element {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%
      );
      animation: shimmer-slide 3s ease-in-out infinite;
      will-change: transform;
    }
  }

  // Dark theme shimmer overlay
  &.theme-dark.animated .shimmer-element::before {
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.15) 50%,
      transparent 100%
    );
  }
}

// Optimized shimmer animation using transform for GPU acceleration
@keyframes shimmer-slide {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

// Base shimmer element - optimized for performance
.shimmer-element {
  border-radius: 6px;

  // Performance optimizations
  contain: layout style paint;
  will-change: transform;

  // Sizes
  &.logo { width: 128px; height: 32px; }
  &.nav-item { width: 80px; height: 16px; }
  &.avatar { width: 32px; height: 32px; border-radius: 50%; }
  &.button { width: 80px; height: 32px; }

  &.sidebar-title { width: 128px; height: 24px; margin-bottom: 8px; }
  &.sidebar-item { width: 96px; height: 16px; margin-bottom: 8px; }

  &.page-title { width: 50%; height: 32px; margin-bottom: 24px; }
  &.card-image { width: 100%; height: 128px; margin-bottom: 16px; }
  &.card-title { width: 75%; height: 16px; margin-bottom: 8px; }
  &.card-text { width: 50%; height: 16px; }

  &.text-line {
    height: 16px;
    margin-bottom: 8px;

    &.full { width: 100%; }
    &.three-quarter { width: 75%; }
    &.half { width: 50%; }
  }

  &.footer-title { width: 96px; height: 24px; margin-bottom: 16px; }
  &.footer-link { width: 80px; height: 16px; margin-bottom: 8px; }
}

// Header
.header {
  padding: 16px 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

// Main container
.main-container {
  flex: 1;
  display: flex;
}

// Sidebar
.sidebar {
  width: 250px;
  padding: 24px;

  &.left-sidebar {
    border-right: 1px solid;
  }

  &.right-sidebar {
    border-left: 1px solid;
  }

  .sidebar-section {
    margin-bottom: 24px;
  }
}

// Body
.body {
  flex: 1;
  padding: 24px;

  .content-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
  }

  .content-card {
    padding: 16px;
  }

  .text-content {
    margin-top: 24px;
  }
}

// Footer
.footer {
  padding: 32px 24px;

  .footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }
}

// Optimized responsive design with reduced complexity
.layout-shimmer {
  // Container queries for split screen scenarios - simplified for performance
  container-type: inline-size;

  @container (max-width: 400px) {
    .sidebar {
      width: 150px;
      padding: 12px;
    }

    .body {
      padding: 12px;

      .content-grid {
        grid-template-columns: 1fr;
        gap: 8px;
      }
    }

    // Simplified shimmer scaling for performance
    .shimmer-element {
      transform: scale(0.75);
      transform-origin: left top;
    }
  }
}

// Simplified responsive design for better performance
@media (max-width: 768px) {
  .layout-shimmer {
    .sidebar {
      display: none;
    }

    .body .content-grid {
      grid-template-columns: 1fr;
    }

    .footer .footer-content {
      grid-template-columns: repeat(2, 1fr);
    }

    // Use transform scaling for better performance
    .shimmer-element {
      transform: scale(0.85);
      transform-origin: left top;
    }
  }
}

@media (max-width: 480px) {
  .layout-shimmer {
    .footer .footer-content {
      grid-template-columns: 1fr;
    }

    // Further scaling for very small screens
    .shimmer-element {
      transform: scale(0.7);
      transform-origin: left top;
    }
  }
}

// Optimized height-based responsive scaling
@media (max-height: 600px) {
  .layout-shimmer {
    .header { padding: 12px 16px; }
    .sidebar { padding: 16px; }
    .body { padding: 16px; }
    .footer { padding: 20px 16px; }

    // Use transform for better performance
    .shimmer-element.card-image {
      transform: scaleY(0.8);
    }
  }
}

@media (max-height: 400px) {
  .layout-shimmer {
    .header { padding: 8px 12px; }
    .sidebar { padding: 12px; }
    .body { padding: 12px; }
    .footer { padding: 16px 12px; }

    // Further height scaling
    .shimmer-element.card-image {
      transform: scaleY(0.6);
    }
  }
}
